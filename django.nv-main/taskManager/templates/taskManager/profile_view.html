{% extends 'taskManager/base_backend.html' %}

{% block content %}

<!--main content start-->
<section class="wrapper">
        <section class="panel col-md-10">
            <header class="panel-heading">
                {{user.first_name}} {{user.last_name}}
            </header>

        </section>
    <div class="row">
        <div class="col-lg-6">
            <section class="panel">
                <header class="panel-heading"{{user.first_name}} {{user.last_name}}</header>
                <div class="panel-body">
	                <section class="panel">
	                    <div class="panel-body">
	                        <div class="row p-details">
	                            <div class="col-md-9">
	                            	<p><span class="bold">Role: </span>{{role}}</p>
	                                <p><span class="bold">Email:</span> <a href="mailto:{{user.email}}">{{user.email}}</a></p>
	                                <p><span class="bold">Projects</span><br />
	                                <dl class="dl-horizontal">
	                                	{% for proj in project_list %}
	                                		<dt><small>{{proj.title}}</small></dt>
	                                		<dd><small>{{proj.text}}</small></dd>
	                                	{% endfor %}
	                                </dl>
	                                </p>
	                            </div>
	                            <div class="col-md-3">
	                            	<p><span><small>Profile Picture</small></span></p>
	                            	<p><img style="max-width: 100%; max-height:500px;" src="{% url 'taskManager:download_profile_pic' user.id %}" /></p>
	                            </div>
	                        </div>
	                    </div>
	                </section>
                </div>
            </section>
        </div>
    </div>
</section>
{% endblock %}