  {% extends 'taskManager/base_backend.html' %}
{% block content %}

<!--main content start-->

    <section class="wrapper site-min-height">
        <!-- page start-->
        <section class="panel">
          {% include 'taskManager/messages.html'%}
            <header class="panel-heading">
                Projects
                <span class="pull-right">
                  <a href="{% url 'taskManager:dashboard' %}" class="btn btn-warning btn-xs"><i class="fa fa-refresh"></i> Refresh</a>
                </span>
            </header>

            <table class="table table-hover p-table">
                <thead>
                <tr>
                    <th>Project</th>
                    <th>Members</th>
                    <th>Progress</th>
                </tr>
                </thead>
                <tbody>
                  {% if project_list %}

                    {% for proj in project_list %}
                      <tr>
                          <td class="p-name">
                              <a href="{% url 'taskManager:project_details' proj.id %}">{{proj.title}}</a>
                              <br>
                              <small>Due {% if proj.is_overdue %}
                            <b style="color:red">{{proj.due_date}}</b>
                          {% else %}
                            {{proj.due_date}}
                          {% endif %}</small>
                          </td>
                          <td class="p-team">
                            {% for u in proj.users_assigned.all%}
                              <a href="{% url 'taskManager:profile_view' u.id %}"><img alt="image" class="" src="{% url 'taskManager:download_profile_pic' u.id %}"></a>
                            {% endfor %}
                          </td>
                          <td class="p-progress">
                              <div class="progress progress-xs">
                                  <div style="width: {{proj.percent_complete}}%;" class="progress-bar progress-bar-success"></div>
                              </div>
                              <small>{{proj.percent_complete}}% Complete</small>
                          </td>
                      </tr>
                    {% endfor %}
                  {% else %}
                    <p> No projects are available.</p>  
                  {% endif %}
              </tbody>
            </table>
	</section>
        <section class="panel">
	    <header class="panel-heading">
                Tasks
            </header>
            <table class="table table-hover p-table">
                <thead>
                <tr>
                    <th>Task</th>
                    <th>Project</th>
                    <th>Created</th>
                    <th>Due</th>
                    <th>Actions</th>
                </tr>
                </thead>
                <tbody>
                  {% if task_list %}
                    {% for task in task_list %}
                      <tr>
                          <td>
                              <a href="{% url 'taskManager:task_details' project_id=task.project.id task_id=task.id %}">{{task.title}}</a>
                          </td>
                          <td>
                              <a href="{% url 'taskManager:project_details' task.project.id %}">{{task.project.title}}</a>
                          </td>
                          <td>
                              {{task.start_date}}
                          </td>
                          <td>
                              {{task.due_date}}
                          </td>
                          <td>
                              <a href="{% url 'taskManager:task_details' project_id=task.project.id task_id=task.id %}" class="btn btn-primary btn-xs"><i class="fa fa-folder"></i> View </a>
                              <a href="{% url 'taskManager:task_edit' project_id=task.project.id task_id=task.id %}" class="btn btn-info btn-xs"><i class="fa fa-pencil"></i> Edit </a>
                              <a href="{% url 'taskManager:task_delete' project_id=task.project.id task_id=task.id %}" class="btn btn-danger btn-xs"><i class="fa fa-trash-o"></i> Delete </a>
                          </td>
                      </tr>
                    {% endfor %}
                  {% else %}
                    <div class="col-md-12">
                      <p>No tasks are assigned to you.</p>
                    </div>
                  {% endif %}
              </tbody>
            </table>

        </section>
        <!-- page end-->
    </section>
        </section>
        <!-- page end-->
    </section>

<!--main content end-->



{% endblock %}