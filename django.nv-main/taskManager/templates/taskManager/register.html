
{% extends 'taskManager/base.html' %}
      
        {% block content %}
        {% if registered %}
            <p><strong>Thanks for registering!</strong></p>
            <p><a href="/taskManager/login">Return to the homepage.</a></p>
        {% else %}
        
          <div class="col-lg-7 col-sm-7 address">
               <h4>Registration</h4>
               <div class="contact-form">
                   <form method="post" role="form" action="/taskManager/register/" enctype="multipart/form-data">
                      {% csrf_token %}
                     <div class="form-group">
                      <!-- {{ user_form }} -->
                        <tr>
                           <th><label for="id_username">Username:</label></th>
                           <td><input class="form-control" id="id_username" name="username" type="text" /></td>
                        </tr>
                        <tr>
                           <th><label for="id_first_name">First name:</label></th>
                           <td><input class="form-control" id="id_first_name" name="first_name" type="text" /></td>
                        </tr>
                        <tr>
                           <th><label for="id_last_name">Last name:</label></th>
                           <td><input class="form-control" id="id_last_name" name="last_name" type="text" /></td>
                        </tr>
                        <tr>
                           <th><label for="id_email">Email:</label></th>
                           <td><input class="form-control" id="id_email" name="email" type="text" /></td>
                        </tr>
                        <tr>
                           <th><label for="id_password">Password:</label></th>
                           <td><input class="form-control" id="id_password" name="password" type="password" /></td>
                        </tr>
                     </div>  
                       <button class="btn btn-danger" type="submit">Register</button>
                   </form>
          
               </div>
          </div>
        
        {% endif %}
        
        {%  endblock %}
      