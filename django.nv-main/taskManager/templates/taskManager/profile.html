{% extends 'taskManager/base_backend.html' %}

{% block content %}

<!--main content start-->
<section class="wrapper">
    <div class="row">
        <div class="col-lg-6">
            <section class="panel">
         		{% include 'taskManager/messages.html'%}
                <header class="panel-heading">Edit Profile ({{user.username}})</header>
                <div class="panel-body">
                    <form method="post" role="form" action="/taskManager/profile/{{user.id}}" enctype="multipart/form-data">
                        <div class="form-group col-lg-7 col-sm-7">
                            <label>Username</label>
                            <input name="username" class="form-control" value ="{{user.username}}">
                        </div>
                        <div class="form-group col-lg-7 col-sm-7">
                            <label>First Name</label>
                            <input name="first_name" class="form-control" value ="{{user.first_name}}">
                        </div>
                        <div class="form-group col-lg-7 col-sm-7">
                            <label>Last Name</label>
                            <input name="last_name" class="form-control" value="{{user.last_name}}">
                        </div>
                        <div class="form-group col-lg-7 col-sm-7">
                            <label>Email</label>
                            <input name="email" class="form-control" value="{{user.email}}">
                        </div>
                        <div class="form-group col-lg-7 col-sm-7">
                            <label>Icon</label>
                            <p><img style="max-width: 200px;" src="{% url 'taskManager:download_profile_pic' user.id %}" /></p>
                                <input class="btn btn-sm btn-info" id="picture" name="picture" type="file">
                            <hr />
                        </div>
                        <div class="form-group col-lg-7 col-sm-7">
                            <button type="submit" class="btn btn-info">Save</button>
                        </div>
                    </form>
                </div>
            </section>
        </div>
    </div>
</section>
{% endblock %}
