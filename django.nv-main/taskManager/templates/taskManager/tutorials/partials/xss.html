 <section class="wrapper site-min-height">
     <!-- page start-->
     <div class="row">
       <div class="col-md-9">
     <div class="tab-content">
     <div class="tab-pane active" id="tab_1">
         <div class="panel-group" id="accordion1">
             <div class="panel panel-default">
                 <div class="panel-heading">
                     <h4 class="panel-title">
                         <a href="#accordion1_1" data-parent="#accordion1" data-toggle="collapse" class="accordion-toggle">
                             Cross-Site Scripting (XSS)
                         </a>
                     </h4>
                 </div>
                 <div class="panel-collapse collapse in" id="accordion1_1">
                     <div class="panel-body">
                       <h6><strong>Description</strong></h6>
                        <p>
                          Cross-Site Scripting occurs when user-supplied data is unsafely rendered back in an application. If the user-supplied data can be used to create new HTML or JavaScript, the user has control over the application behavior at the presentation layer.
                        </p>
                     </div>
                 </div>
             </div>
             <div class="panel panel-danger">
                 <div class="panel-heading">
                     <h4 class="panel-title">
                         <a href="#accordion1_2" data-parent="#accordion1" data-toggle="collapse" class="accordion-toggle">
                            Bug
                         </a>
                     </h4>
                 </div>
                 <div class="panel-collapse collapse" id="accordion1_2">
                     <div class="panel-body">
                         The application renders back a user's username (user-supplied data) by leveraging a call to the safe method within the view. This means the content will not be HTML encoded and will be interpreted as valid JS or HTML code.
                     </div>
                     <pre><code>&lt;a data-toggle=&quot;dropdown&quot; class=&quot;dropdown-toggle&quot; href=&quot;#&quot;&gt;
  &lt;span class=&quot;username&quot;&gt;&#123;&#123; user.username|safe &#125;&#125;&lt;/span&gt;
  &lt;b class=&quot;caret&quot;&gt;&lt;/b&gt;
&lt;/a&gt;</code></pre>
                 </div>
             </div>
             <div class="panel panel-success">
                 <div class="panel-heading">
                     <h4 class="panel-title">
                         <a href="#accordion1_3" data-parent="#accordion1" data-toggle="collapse" class="accordion-toggle">
                            Solution
                         </a>
                     </h4>
                 </div>
                 <div class="panel-collapse collapse" id="accordion1_3">
                     <div class="panel-body">
                         <p> Remove the safe method within the backend/base.html and tutorials/base.html files. Consider setting <b>X-XSS-Protection</b> in your webserver configuration as well. This Header will configure clients' browser XSS protection to always block XSS attacks it detects.
                         Django can automatically do this if you set <b>SECURE_BROWSER_XSS_FILTER</b> to True in the configuration. While this won't protect against all XSS attacks, it's still a useful addition to your security.</p>
                     </div>
                 </div>
             </div>
             <div class="panel panel-warning">
                 <div class="panel-heading">
                     <h4 class="panel-title">
                         <a href="#accordion1_4" data-parent="#accordion1" data-toggle="collapse" class="accordion-toggle">
                             Hint
                         </a>
                     </h4>
                 </div>
                 <div class="panel-collapse collapse" id="accordion1_4">
                     <div class="panel-body">
                         I noticed the application puts my username in the upper right hand corner once authenticated. Interesting.
                     </div>
                 </div>
             </div>
         </div>
     </div>
     </div>
     <!-- page end-->
 </section>
