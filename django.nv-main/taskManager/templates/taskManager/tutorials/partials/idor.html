 <section class="wrapper site-min-height">
     <!-- page start-->
     <div class="row">
       <div class="col-md-9">
     <div class="tab-content">
     <div class="tab-pane active" id="tab_1">
         <div class="panel-group" id="accordion1">
             <div class="panel panel-default">
                 <div class="panel-heading">
                     <h4 class="panel-title">
                         <a href="#accordion1_1" data-parent="#accordion1" data-toggle="collapse" class="accordion-toggle">
                             Insecure Direct Object Reference (IDOR)
                         </a>
                     </h4>
                 </div>
                 <div class="panel-collapse collapse  in" id="accordion1_1">
                     <div class="panel-body">
                       <h6><strong>Description</strong></h6>
                        <p>
                          Insecure Direct Object Reference is a class of vulnerability where attackers are able to access pages they are not meant to have access to simply by changing parameter values. This attack results from a combination of insufficient access controls, and overly excessive client trust (excess/unneccesary request parameters).
                        </p>
                     </div>
                 </div>
             </div>
             <div class="panel panel-danger">
                 <div class="panel-heading">
                     <h4 class="panel-title">
                         <a href="#accordion1_2" data-parent="#accordion1" data-toggle="collapse" class="accordion-toggle">
                            Bug
                         </a>
                     </h4>
                 </div>
                 <div class="panel-collapse collapse" id="accordion1_2">
                    <div class="panel-body">
                        Insecure Direct Object References can be found throughout the TaskManager source code. Straightforward examples can be found in most view functions that create, edit, or delete notes/tasks/projects. These view functions all fail to ensure the user is properly authorized to perform the action. You can find the appropriate functions labeled throughout the source code.
                        <br /><br />
                        The example below is the method call for deleting tasks with an associated project. Note the lack of auth checks performed on the <code>task_id</code> before deleting the task.
                    </div>
                    <pre class="line-numbers"><code class="language-python">def task_delete(request, project_id, task_id):
    proj = Project.objects.get(pk = project_id)
    task = Task.objects.get(pk = task_id)
    if proj != None:
        if task != None and task.project == proj:
            task.delete()

    return redirect('/taskManager/' + project_id + '/')</code></pre>
                 </div>
             </div>
             <div class="panel panel-success">
                 <div class="panel-heading">
                     <h4 class="panel-title">
                         <a href="#accordion1_3" data-parent="#accordion1" data-toggle="collapse" class="accordion-toggle">
                            Solution
                         </a>
                     </h4>
                 </div>
                 <div class="panel-collapse collapse" id="accordion1_3">
                     <div class="panel-body">
                         In each function that reads or writes from the database, validate the the user has the appropriate authorization and authentication to perform the action. Within django, there are some <a href="https://docs.djangoproject.com/en/1.9/ref/contrib/auth/#django.contrib.auth.models.User.is_authenticated">useful functions</a> to ensure this, including
                         <code>user.is_authenticated</code> and <code>user.has_perm('permission_name')</code>. django.nV exhibits this functionality in some of the view functions as well, including <code>manage_tasks</code> and <code>manage_projects</code>.
                         <br /><br />
                         In general, user access must always be checked before any information can be added or changed in the database. Don't assume that just because a method is not linked to, it won't be found!
                     </div>
                 </div>
             </div>
             <div class="panel panel-warning">
                 <div class="panel-heading">
                     <h4 class="panel-title">
                         <a href="#accordion1_4" data-parent="#accordion1" data-toggle="collapse" class="accordion-toggle">
                             Hint
                         </a>
                     </h4>
                 </div>
                 <div class="panel-collapse collapse" id="accordion1_4">
                     <div class="panel-body">
                         Do any of these project updates calls even check if I am assigned to the project?
                         <br /><br />
                         Try modifying ID values in requests made to the application. Maybe it will reveal something about how the methods operate. Note that this is typically much easier to achieve using a client side proxy such as Burp Suite or Fiddler, especially with POST requests.
                     </div>
                 </div>
             </div>
         </div>
     </div>
     </div>
     <!-- page end-->
 </section>
