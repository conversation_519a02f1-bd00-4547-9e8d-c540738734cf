 <section class="wrapper site-min-height">
     <!-- page start-->
     <div class="row">
       <div class="col-md-9">
     <div class="tab-content">
     <div class="tab-pane active" id="tab_1">
         <div class="panel-group" id="accordion1">
             <div class="panel panel-default">
                 <div class="panel-heading">
                     <h4 class="panel-title">
                         <a href="#accordion1_1" data-parent="#accordion1" data-toggle="collapse" class="accordion-toggle">
                             Using Components with Known Vulnerabilities
                         </a>
                     </h4>
                 </div>
                 <div class="panel-collapse collapse  in" id="accordion1_1">
                     <div class="panel-body">
                       <h6><strong>Description</strong></h6>
                        <p>
                          Almost any medium-large scale software project uses several external libraries to simplify development. In web development, libraries are common on the server (Django, Ruby on Rails, etc) and on the client (jQuery, AngularJS, etc). If such libraries are used, it is important to make sure they are up to date to avoid allowing your application to become vulnerable as bugs are discovered.<br />
                        <br />
                          Virtually every application has issues with vulnerable components because most development teams do not focus on ensuring their components are up to date. In many cases, the developers do not even know all the components they are using, due to unexpected factors like component dependencies.
                        </p>
                     </div>
                 </div>
             </div>
             <div class="panel panel-danger">
                 <div class="panel-heading">
                     <h4 class="panel-title">
                         <a href="#accordion1_2" data-parent="#accordion1" data-toggle="collapse" class="accordion-toggle">
                            Bug
                         </a>
                     </h4>
                 </div>
                 <div class="panel-collapse collapse" id="accordion1_2">
                    <div class="panel-body">
                        The application validation for redirects did not correctly validate some malformed URLs, which are accepted by some browsers. This allows a user to be redirected to an unsafe URL unexpectedly. In turn, this allows remote attackers to conduct cross-site scripting attacks via a control character in a URL. 
                    </div>
                 </div>
             </div>
             <div class="panel panel-success">
                 <div class="panel-heading">
                     <h4 class="panel-title">
                         <a href="#accordion1_3" data-parent="#accordion1" data-toggle="collapse" class="accordion-toggle">
                            Solution
                         </a>
                     </h4>
                 </div>
                 <div class="panel-collapse collapse" id="accordion1_3">
                     <div class="panel-body">
                         The validation in <code>is_safe_url()</code> has been tightened in django versions before 1.7.7, and 1.8x before 1.8c1. Make sure that you have the latest version of Django installed. If you're using <code>pip</code>, you can check for outdated packages using the <code>pip list --outdated</code> command.<br/>
                         <br/>
                         You can view the Django commit fixing this issue in <a href="https://github.com/django/django/commit/e7b0cace455c2da24492660636bfd48c45a19cdf">version 1.7</a> or <a href="https://github.com/django/django/commit/255449c1ee61c14778658caae8c430fa4d76afd6">version 1.8</a>.
                     </div>
                 </div>
             </div>
             <div class="panel panel-warning">
                 <div class="panel-heading">
                     <h4 class="panel-title">
                         <a href="#accordion1_4" data-parent="#accordion1" data-toggle="collapse" class="accordion-toggle">
                             Hint
                         </a>
                     </h4>
                 </div>
                 <div class="panel-collapse collapse" id="accordion1_4">
                     <div class="panel-body">
                         Make sure that you have the latest version of Django installed.
                     </div>
                 </div>
             </div>
         </div>
     </div>
     </div>
     <!-- page end-->
 </section>
