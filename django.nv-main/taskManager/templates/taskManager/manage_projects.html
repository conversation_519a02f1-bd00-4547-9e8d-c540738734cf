{% extends 'taskManager/base_backend.html' %}
{% block content %}
<section class="wrapper">
<div class="row">
  <div class="col-lg-6">
      <section class="panel">
          <header class="panel-heading">
              Assign user to project
          </header>
          <div class="panel-body">
              <form method="post" role="form">
                  {% csrf_token %}
                  <div class="form-group">
                      <label>User</label>
                        <select name="userid" id="userid">
                        {% for user in users %}
                            <option value="{{user.id}}">{{user.get_full_name}} ({{user}})</option>
                        {% endfor %}
                        </select>
                  </div>
                  <div class="form-group">
                      <label>Project</label>
                        <select name="projectid" id="projectid">
                        {% for project in projects %}
                            <option value="{{project.id}}">{{project.title}}</option>
                        {% endfor %}
                        </select>
                  </div>
                  <div class="form-group">
                    <button type="submit" class="btn btn-info">Update</button>
                  </div>
              </form>

          </div>
      </section>
  </div>
  
</div>  
</section>
             
{% endblock %}