[{"fields": {"title": "News", "completed": false, "start_date": "2014-10-07T20:25:57.130Z", "text": "What's news?", "project": 1}, "model": "taskManager.task", "pk": 1}, {"fields": {"title": "Django", "completed": false, "start_date": "2014-10-07T21:12:13Z", "text": "Djangooo!", "project": 1}, "model": "taskManager.task", "pk": 2}, {"fields": {"title": "Hygiene", "completed": false, "start_date": "2014-10-10T20:57:02Z", "text": "First Task: brush teeth", "project": 1}, "model": "taskManager.task", "pk": 3}, {"fields": {"title": "Dress yourself", "completed": false, "start_date": "2014-10-10T20:57:13Z", "text": "Second Task: get dressed", "project": 1}, "model": "taskManager.task", "pk": 4}, {"fields": {"title": "Eat!", "completed": false, "start_date": "2014-10-10T20:57:24Z", "text": "Third: Eat Dinner", "project": 1}, "model": "taskManager.task", "pk": 5}, {"fields": {"title": "Get up", "completed": false, "start_date": "2014-10-10T21:16:37Z", "text": "rise and shine", "project": 2}, "model": "taskManager.task", "pk": 6}, {"fields": {"title": "new!", "completed": false, "start_date": "2014-10-28T17:10:11.970Z", "text": "new", "project": 1}, "model": "taskManager.task", "pk": 7}, {"fields": {"title": "what?", "completed": false, "start_date": "2014-10-28T17:45:21.392Z", "text": "what what", "project": 1}, "model": "taskManager.task", "pk": 8}, {"fields": {"title": "CAKE", "completed": false, "start_date": "2014-10-28T17:49:24.623Z", "text": "Eat the cake", "project": 2}, "model": "taskManager.task", "pk": 9}, {"fields": {"title": "How a-boot that", "completed": false, "start_date": "2014-10-28T17:50:48.961Z", "text": "The CN Tower (French: Tour CN) is a 553.33 m-high (1,815.4 ft) concrete communications and observation tower in Downtown Toronto, Ontario, Canada.[3][6] Built on the former Railway Lands, it was completed in 1976, becoming the world's tallest free-standing structure and world's tallest tower at the time. It held both records for 34 years until the completion of Burj Khalifa and Canton Tower in 2010.", "project": 2}, "model": "taskManager.task", "pk": 10}, {"fields": {"title": "nonsense", "completed": false, "start_date": "2014-10-28T19:41:32.529Z", "text": "kweflkwef", "project": 6}, "model": "taskManager.task", "pk": 11}, {"fields": {"title": "$, yo", "completed": false, "start_date": "2014-10-28T20:52:00.339Z", "text": "jdmoney", "project": 2}, "model": "taskManager.task", "pk": 12}, {"fields": {"title": "Advertising", "completed": false, "start_date": "2014-11-11T18:31:01.306Z", "text": "Create TV ad", "project": 7}, "model": "taskManager.task", "pk": 13}, {"fields": {"title": "Advertising (radio)", "completed": false, "start_date": "2014-11-11T18:31:16.568Z", "text": "Record radio commercial", "project": 7}, "model": "taskManager.task", "pk": 14}, {"fields": {"title": "Marketing (tweeeterz)", "completed": false, "start_date": "2014-11-11T18:31:31.987Z", "text": "Get a young person who knows what Tweeter is", "project": 7}, "model": "taskManager.task", "pk": 15}, {"fields": {"title": "Outreach", "completed": false, "start_date": "2014-11-11T18:31:52.686Z", "text": "Contact local department store representatives", "project": 7}, "model": "taskManager.task", "pk": 16}, {"fields": {"title": "Number", "completed": false, "start_date": "2014-12-11T08:51:20.711Z", "text": "1", "project": 6}, "model": "taskManager.task", "pk": 17}, {"fields": {"title": "Test", "completed": false, "start_date": "2014-12-11T19:04:35.175Z", "text": "This is a test task", "project": 8}, "model": "taskManager.task", "pk": 18}, {"fields": {"title": "Lumber<PERSON>", "completed": false, "start_date": "2014-12-12T19:10:31.126Z", "text": "find a tree", "project": 4}, "model": "taskManager.task", "pk": 19}, {"fields": {"title": "XSS n Stuff", "completed": false, "start_date": "2014-12-12T19:46:37.723Z", "text": "<img alt=<script>alert(document.cookie);</script>>", "project": 4}, "model": "taskManager.task", "pk": 20}, {"fields": {"title": "asdf", "completed": false, "start_date": "2014-12-18T02:23:05.081Z", "text": "asdf", "project": 3}, "model": "taskManager.task", "pk": 21}]