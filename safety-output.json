Safety 3.5.2 scanning /home/<USER>/Projects/CAISP/GitLab
2025-06-26 01:34:11 UTC

Account: <EMAIL> 
 Environment: Stage.development
 Scan policy: None, using Safety CLI default policies

Python detected. Found 1 Python environment and 1 Python requirement file

✅ venv/pyvenv.cfg: No issues found.

Dependency vulnerabilities detected:

📝 django.nv-main/requirements.txt:

 Django==3.0 [47 vulnerabilities found, including 6 critical severity vulnerabilities]                        
 Update Django==3.0 to Django==4.2.22 to fix 47 vulnerabilities, including 6 critical severity vulnerabilities
 🛑                                                                                                           
 Versions of Django with no known vulnerabilities: 5.2.3, 5.2.2, 5.2rc1, 5.2b1, 5.2a1, 5.1.11, 5.1.10, 4.2.23 
 Learn more: https://data.safetycli.com/p/pypi/django/eda/?from=3.0&to=4.2.22                                 

--------------------------------------------------------------------------------------------------------------
Apply Fixes
--------------------------------------------------------------------------------------------------------------

Run `safety scan --apply-fixes` to update these packages and fix these vulnerabilities. Documentation, 
limitations, and configurations for applying automated fixes: 
https://docs.safetycli.com/safety-docs/vulnerability-remediation/applying-fixes

Alternatively, use your package manager to update packages to their secure versions. Always check for breaking
changes when updating packages.
Tip: For more detailed output on each vulnerability, add the `--detailed-output` flag to safety scan.

--------------------------------------------------------------------------------------------------------------

Tested 45 dependencies for security issues using default Safety CLI policies
47 vulnerabilities found, 0 ignored due to policy.
1 fix suggested, resolving 47 vulnerabilities.

